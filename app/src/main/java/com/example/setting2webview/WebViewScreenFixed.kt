package com.example.setting2webview

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.util.Log
import android.view.View
import android.webkit.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView

/**
 * 修复版WebView - 专门解决白屏问题
 * 使用更激进的方法防止页面变白屏
 */
@SuppressLint("SetJavaScriptEnabled")
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WebViewScreenFixed(
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    var isLoading by remember { mutableStateOf(true) }
    var webView by remember { mutableStateOf<WebView?>(null) }
    var pageTitle by remember { mutableStateOf("加载中...") }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
    ) {
        // 顶部标题栏
        TopAppBar(
            title = {
                Text(
                    text = pageTitle,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium
                )
            },
            navigationIcon = {
                IconButton(onClick = onBackClick) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回"
                    )
                }
            },
            colors = TopAppBarDefaults.topAppBarColors(
                containerColor = MaterialTheme.colorScheme.surface,
                titleContentColor = MaterialTheme.colorScheme.onSurface
            )
        )
        
        // WebView容器
        Box(modifier = Modifier.fillMaxSize()) {
            AndroidView(
                factory = { context ->
                    WebView(context).apply {
                        webView = this
                        
                        // 防止WebView被系统回收
                        setLayerType(View.LAYER_TYPE_HARDWARE, null)
                        keepScreenOn = true
                        
                        // 激进的WebView设置
                        settings.apply {
                            // 基础JavaScript设置
                            javaScriptEnabled = true
                            javaScriptCanOpenWindowsAutomatically = false
                            
                            // 存储设置
                            domStorageEnabled = true
                            @Suppress("DEPRECATION")
                            databaseEnabled = true
                            
                            // 缓存设置 - 强制使用缓存
                            cacheMode = WebSettings.LOAD_CACHE_ELSE_NETWORK
                            
                            // 网络和安全设置
                            mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                            allowFileAccess = false
                            allowContentAccess = false
                            @Suppress("DEPRECATION")
                            allowUniversalAccessFromFileURLs = false
                            @Suppress("DEPRECATION")
                            allowFileAccessFromFileURLs = false
                            
                            // 用户代理 - 使用桌面版Chrome
                            userAgentString = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
                            
                            // 渲染设置
                            @Suppress("DEPRECATION")
                            setRenderPriority(WebSettings.RenderPriority.HIGH)
                            loadsImagesAutomatically = true
                            blockNetworkImage = false
                            
                            // 布局设置
                            useWideViewPort = true
                            loadWithOverviewMode = true
                            layoutAlgorithm = WebSettings.LayoutAlgorithm.NORMAL
                            
                            // 缩放设置
                            setSupportZoom(true)
                            builtInZoomControls = true
                            displayZoomControls = false
                            
                            // 其他设置
                            setSupportMultipleWindows(false)
                            setGeolocationEnabled(false) // 禁用地理位置
                            mediaPlaybackRequiresUserGesture = false
                            
                            // 字体设置
                            minimumFontSize = 8
                            minimumLogicalFontSize = 8
                            defaultTextEncodingName = "utf-8"
                        }
                        
                        // WebViewClient - 防止页面跳转和重新加载
                        webViewClient = object : WebViewClient() {
                            override fun onPageStarted(view: WebView?, url: String?, favicon: Bitmap?) {
                                super.onPageStarted(view, url, favicon)
                                Log.d("WebViewFixed", "页面开始加载: $url")
                                isLoading = true
                                pageTitle = "加载中..."
                            }
                            
                            override fun onPageFinished(view: WebView?, url: String?) {
                                super.onPageFinished(view, url)
                                Log.d("WebViewFixed", "页面加载完成: $url")
                                
                                // 注入防白屏JavaScript
                                view?.evaluateJavascript("""
                                    (function() {
                                        // 防止页面被清空
                                        var originalWrite = document.write;
                                        document.write = function() {
                                            console.log('阻止document.write调用');
                                        };
                                        
                                        // 防止页面重定向
                                        var originalReplace = window.location.replace;
                                        window.location.replace = function(url) {
                                            console.log('阻止页面重定向到: ' + url);
                                        };
                                        
                                        // 监控DOM变化
                                        var observer = new MutationObserver(function(mutations) {
                                            mutations.forEach(function(mutation) {
                                                if (mutation.type === 'childList' && mutation.removedNodes.length > 0) {
                                                    console.log('检测到DOM节点被移除');
                                                }
                                            });
                                        });
                                        
                                        observer.observe(document.body, {
                                            childList: true,
                                            subtree: true
                                        });
                                        
                                        console.log('防白屏脚本已注入');
                                        return document.title || '页面已加载';
                                    })();
                                """) { result ->
                                    Log.d("WebViewFixed", "防白屏脚本执行结果: $result")
                                    pageTitle = result?.replace("\"", "") ?: "显示"
                                }
                                
                                // 延迟隐藏加载指示器
                                view?.postDelayed({
                                    isLoading = false
                                }, 2000) // 延迟2秒
                            }
                            
                            override fun shouldOverrideUrlLoading(view: WebView?, request: WebResourceRequest?): Boolean {
                                val url = request?.url?.toString()
                                Log.d("WebViewFixed", "拦截URL跳转: $url")
                                
                                // 只允许目标域名的页面
                                return if (url?.contains("chat1.horrz.me") == true) {
                                    false // 允许加载
                                } else {
                                    Log.d("WebViewFixed", "阻止跳转到外部URL: $url")
                                    true // 阻止跳转
                                }
                            }
                            
                            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                                super.onReceivedError(view, request, error)
                                Log.e("WebViewFixed", "页面错误: ${error?.description}, URL: ${request?.url}")
                                
                                if (request?.isForMainFrame == true) {
                                    isLoading = false
                                    pageTitle = "加载失败"
                                }
                            }
                            
                            override fun onReceivedHttpError(view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?) {
                                super.onReceivedHttpError(view, request, errorResponse)
                                Log.e("WebViewFixed", "HTTP错误: ${errorResponse?.statusCode}")
                            }
                            
                            @Suppress("DEPRECATION")
                            override fun onReceivedSslError(view: WebView?, handler: SslErrorHandler?, error: android.net.http.SslError?) {
                                Log.w("WebViewFixed", "SSL错误，继续加载: ${error?.toString()}")
                                handler?.proceed()
                            }
                        }
                        
                        // WebChromeClient - 监控页面状态
                        webChromeClient = object : WebChromeClient() {
                            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                                super.onProgressChanged(view, newProgress)
                                Log.d("WebViewFixed", "加载进度: $newProgress%")
                                
                                if (newProgress >= 100) {
                                    view?.postDelayed({
                                        Log.d("WebViewFixed", "进度100%，确认加载完成")
                                    }, 1000)
                                }
                            }
                            
                            override fun onReceivedTitle(view: WebView?, title: String?) {
                                super.onReceivedTitle(view, title)
                                Log.d("WebViewFixed", "页面标题: $title")
                                if (!title.isNullOrBlank() && title != "about:blank") {
                                    pageTitle = title
                                }
                            }
                            
                            override fun onConsoleMessage(consoleMessage: ConsoleMessage?): Boolean {
                                val level = when (consoleMessage?.messageLevel()) {
                                    ConsoleMessage.MessageLevel.ERROR -> "ERROR"
                                    ConsoleMessage.MessageLevel.WARNING -> "WARNING"
                                    else -> "LOG"
                                }
                                Log.d("WebViewFixed", "[$level] ${consoleMessage?.message()}")
                                return true
                            }
                            
                            override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
                                Log.d("WebViewFixed", "JS Alert: $message")
                                result?.confirm()
                                return true
                            }
                        }
                        
                        // 加载目标网页
                        Log.d("WebViewFixed", "开始加载目标网页")
                        loadUrl("https://chat1.horrz.me:8099")
                    }
                },
                modifier = Modifier.fillMaxSize(),
                update = { view ->
                    // 定期检查页面状态
                    view.postDelayed({
                        view.evaluateJavascript("document.body.innerHTML.length") { result ->
                            Log.d("WebViewFixed", "页面内容长度: $result")
                            if (result == "0" || result == "null") {
                                Log.w("WebViewFixed", "检测到页面内容为空，尝试重新加载")
                                view.reload()
                            }
                        }
                    }, 5000) // 5秒后检查
                }
            )
            
            // 加载指示器
            if (isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = androidx.compose.ui.Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = androidx.compose.ui.Alignment.CenterHorizontally
                    ) {
                        CircularProgressIndicator(
                            color = MaterialTheme.colorScheme.primary
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(
                            text = "正在加载网页...",
                            style = MaterialTheme.typography.bodyMedium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Button(
                            onClick = {
                                Log.d("WebViewFixed", "用户点击强制完成加载")
                                isLoading = false
                            }
                        ) {
                            Text("强制完成")
                        }
                    }
                }
            }
        }
    }
}
