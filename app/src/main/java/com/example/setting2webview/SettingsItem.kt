package com.example.setting2webview

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.ui.graphics.vector.ImageVector

/**
 * 设置项数据类
 */
data class SettingsItem(
    val id: String,
    val title: String,
    val description: String,
    val icon: ImageVector,
    val isSpecial: Boolean = false // 标记是否为特殊项目（如显示项）
)

/**
 * 获取所有设置项目
 */
fun getSettingsItems(): List<SettingsItem> {
    return listOf(
        SettingsItem(
            id = "airplane_mode",
            title = "飞行模式",
            description = "关闭所有无线连接",
            icon = Icons.Default.Flight
        ),
        SettingsItem(
            id = "wifi",
            title = "WIFI",
            description = "管理WIFI连接",
            icon = Icons.Default.Wifi
        ),
        SettingsItem(
            id = "bluetooth",
            title = "蓝牙",
            description = "管理蓝牙设备",
            icon = Icons.Default.Bluetooth
        ),
        SettingsItem(
            id = "cellular_network",
            title = "蜂窝网络",
            description = "移动数据和网络设置",
            icon = Icons.Default.SignalCellular4Bar
        ),
        SettingsItem(
            id = "hotspot",
            title = "热点",
            description = "共享网络连接",
            icon = Icons.Default.Router
        ),
        SettingsItem(
            id = "display",
            title = "显示",
            description = "亮度、壁纸、睡眠",
            icon = Icons.Default.Computer,
//            isSpecial = true // 标记为特殊项目
        ),
        SettingsItem(
            id = "system",
            title = "系统",
            description = "语言、时间、备份",
            icon = Icons.Default.Settings
        )
    )
}
