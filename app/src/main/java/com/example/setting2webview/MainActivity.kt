package com.example.setting2webview

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.viewmodel.compose.viewModel
import com.example.setting2webview.ui.theme.Setting2webviewTheme

/**
 * 主Activity
 * 高仿Android系统设置应用
 */
class MainActivity : ComponentActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        setContent {
            Setting2webviewTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    SettingsApp(
                        onAppGoesToBackground = {
                            // 当应用进入后台时的回调
                        }
                    )
                }
            }
        }
    }
}

/**
 * 主应用组合函数
 * 根据当前状态显示不同的界面
 */
@Composable
fun SettingsApp(
    onAppGoesToBackground: () -> Unit = {}
) {
    val viewModel: SettingsViewModel = viewModel()
    val lifecycleOwner = LocalLifecycleOwner.current

    // 监听应用生命周期
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_PAUSE -> {
                    // 应用进入后台时，返回到设置主页面
                    viewModel.onAppGoesToBackground()
                }
                Lifecycle.Event.ON_STOP -> {
                    // 应用停止时，返回到设置主页面
                    viewModel.onAppGoesToBackground()
                }
                else -> {}
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)

        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // 获取当前屏幕状态
    val currentScreen by viewModel.currentScreen
    val settingsItems by viewModel.settingsItems

    // 根据状态显示不同界面
    when (currentScreen) {
        ScreenState.SETTINGS_LIST -> {
            SettingsListScreen(
                settingsItems = settingsItems,
                onItemClick = { item ->
                    viewModel.onSettingsItemClick(item)
                }
            )
        }

        ScreenState.WEBVIEW -> {
            WebViewScreen(
                onBackClick = {
                    viewModel.navigateToSettingsList()
                }
            )
        }
    }
}