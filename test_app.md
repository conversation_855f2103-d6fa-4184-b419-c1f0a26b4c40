# 应用测试指南

## 测试环境准备

### 1. 安装APK到设备
```bash
# 确保设备已连接并启用USB调试
adb devices

# 安装应用
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. 启动应用
```bash
# 通过ADB启动应用
adb shell am start -n com.example.setting2webview/.MainActivity
```

## 功能测试清单

### ✅ 主界面测试
- [ ] 应用启动正常，显示设置列表
- [ ] 界面布局符合Android设计规范
- [ ] 所有设置项目都正确显示（7个项目）
- [ ] 图标和文字显示正确
- [ ] 滚动功能正常

### ✅ 设置项目测试

#### 一般选项测试
- [ ] 点击"飞行模式" - 应有点击反馈，不跳转
- [ ] 点击"WIFI" - 应有点击反馈，不跳转  
- [ ] 点击"蓝牙" - 应有点击反馈，不跳转
- [ ] 点击"蜂窝网络" - 应有点击反馈，不跳转
- [ ] 点击"热点" - 应有点击反馈，不跳转
- [ ] 点击"系统" - 应有点击反馈，不跳转

#### 特殊选项测试
- [ ] 点击"显示" - 应打开WebView界面
- [ ] WebView正确加载 https://chat1.horrz.me:8099
- [ ] 网页内容正常显示
- [ ] 网页交互功能正常
- [ ] 返回按钮功能正常

### ✅ 生命周期测试
- [ ] 在WebView界面按Home键，再回到应用 - 应返回设置主页
- [ ] 在WebView界面切换到其他应用，再回来 - 应返回设置主页
- [ ] 在设置主页按Home键，再回到应用 - 应保持在设置主页
- [ ] 应用内存使用正常，无明显泄漏

### ✅ UI/UX测试
- [ ] 界面响应速度正常
- [ ] 点击反馈及时
- [ ] 过渡动画流畅
- [ ] 文字清晰可读
- [ ] 图标显示正确
- [ ] 颜色搭配合理

### ✅ 网络功能测试
- [ ] 有网络时WebView正常加载
- [ ] 无网络时WebView显示错误页面
- [ ] 网络恢复后WebView能正常工作
- [ ] WebView支持JavaScript
- [ ] WebView支持现代Web功能

## 性能测试

### 内存使用
```bash
# 查看应用内存使用
adb shell dumpsys meminfo com.example.setting2webview
```

### CPU使用
```bash
# 查看应用CPU使用
adb shell top | grep setting2webview
```

### 网络使用
```bash
# 查看网络使用情况
adb shell dumpsys netstats | grep setting2webview
```

## 日志查看

### 应用日志
```bash
# 查看应用日志
adb logcat | grep setting2webview
```

### 系统日志
```bash
# 查看系统相关日志
adb logcat | grep -E "(ActivityManager|WindowManager)" | grep setting2webview
```

## 常见问题排查

### 1. 应用无法启动
- 检查设备Android版本是否支持（需要API 34+）
- 检查APK是否正确安装
- 查看logcat错误信息

### 2. WebView无法加载
- 检查网络连接
- 检查网络权限是否正确添加
- 确认目标网址可访问

### 3. 生命周期异常
- 检查ViewModel状态管理
- 查看Activity生命周期日志
- 确认DisposableEffect正常工作

### 4. 界面显示异常
- 检查Compose版本兼容性
- 确认主题配置正确
- 查看布局相关错误

## 测试报告模板

```
测试日期：____年____月____日
测试设备：________________
Android版本：_____________
测试人员：_______________

主界面功能：□ 通过 □ 失败
设置项点击：□ 通过 □ 失败  
WebView功能：□ 通过 □ 失败
生命周期：□ 通过 □ 失败
性能表现：□ 良好 □ 一般 □ 差

问题记录：
1. ________________________
2. ________________________
3. ________________________

总体评价：□ 优秀 □ 良好 □ 需改进

建议：
_________________________________
_________________________________
```

## 自动化测试

如需要进行自动化测试，可以考虑使用：
- Espresso UI测试框架
- UI Automator跨应用测试
- Compose测试工具

测试完成后，请确保应用满足所有功能要求并且用户体验良好。
