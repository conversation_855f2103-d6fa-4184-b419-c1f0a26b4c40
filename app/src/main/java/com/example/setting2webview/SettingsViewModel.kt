package com.example.setting2webview

import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel

/**
 * 设置应用的ViewModel
 * 管理应用的状态和导航
 */
class SettingsViewModel : ViewModel() {
    
    // 当前显示的屏幕状态
    private val _currentScreen = mutableStateOf(ScreenState.SETTINGS_LIST)
    val currentScreen: State<ScreenState> = _currentScreen
    
    // 设置项目列表
    private val _settingsItems = mutableStateOf(getSettingsItems())
    val settingsItems: State<List<SettingsItem>> = _settingsItems
    
    /**
     * 处理设置项点击事件
     */
    fun onSettingsItemClick(item: SettingsItem) {
        when (item.id) {
            "display" -> {
                // 显示项目，打开WebView
                _currentScreen.value = ScreenState.WEBVIEW
            }
            else -> {
                // 其他项目，只是简单响应点击
                // 这里可以添加Toast提示或其他反馈
                println("点击了: ${item.title}")
            }
        }
    }
    
    /**
     * 返回到设置列表
     */
    fun navigateToSettingsList() {
        _currentScreen.value = ScreenState.SETTINGS_LIST
    }
    
    /**
     * 应用进入后台时调用
     */
    fun onAppGoesToBackground() {
        // 自动返回到设置主页面
        navigateToSettingsList()
    }
}

/**
 * 屏幕状态枚举
 */
enum class ScreenState {
    SETTINGS_LIST,  // 设置列表页面
    WEBVIEW        // WebView页面
}
